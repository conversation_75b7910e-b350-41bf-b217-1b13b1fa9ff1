import { Module } from '@nestjs/common';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { join } from 'path';
import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { MailerModule } from 'nestjs-mailer';
import { MulterModule } from '@nestjs/platform-express';
import { CacheModule } from '@nestjs/cache-manager';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import  configuration  from './config';

import { LoggerModule } from './common/logger/logger.module';
import { CommonModule } from './common/common.module';
import { CoreModule } from './core/core.module';
import { EventsModule } from './events/events.module';
import { MailModule } from './core/notification/mail/mail.module';
import { AuthorizationModule } from './core/authorization/authorization.module';

import { ProfileContextInterceptor } from './profile-context/profile-context.interceptor';
import { RolePermissionGuard } from './core/authorization/role/rolePermissionguard';

@Module({
  imports: [
    LoggerModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<object>('database'),
        autoLoadEntities: true,
      }),
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    AutomapperModule.forRoot({
      strategyInitializer: classes(),
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        config: configService.get('mails'),
      }),
      inject: [ConfigService],
    }),
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
    CacheModule.register({ isGlobal: true }),
    MailModule,
    AuthorizationModule,
    CommonModule,
    CoreModule,
    EventsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ProfileContextInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: RolePermissionGuard,
    },
  ],
})
export class AppModule {}
