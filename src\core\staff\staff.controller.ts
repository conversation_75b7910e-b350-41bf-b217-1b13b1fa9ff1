import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { StaffService } from './staff.service';
import { LoggerService } from '@common/logger/logger.service';

@ApiTags('Staff Endpoints')
@Controller({
  path: 'staff',
  version: '1',
})
export class StaffController {
  constructor(
    private readonly staffService: StaffService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(StaffController.name);
  }

  // @Public()
  // @ApiCreatedResponse({ type: Staff })
  // @ApiBadRequestResponse()
  // @Post()
  // async create(@Body() createStaffDto: CreateStaffDto) {
  //   return this.staffService.create(createStaffDto);
  // }
  //
  // @Public()
  // @ApiOkResponse({ type: Staff, isArray: true })
  // @ApiNotFoundResponse()
  // @Get()
  // async findAll(): Promise<Staff[]> {
  //   return this.staffService.findAll();
  // }
  //
  // @Public()
  // @ApiOkResponse({ type: Staff })
  // @ApiNotFoundResponse()
  // @Get(':id')
  // async findOne(@Param('id') id: string): Promise<Staff | string> {
  //   return this.staffService.findOne(+id);
  // }
  //
  // @Public()
  // @ApiOkResponse({ description: 'Staff updated successfully' })
  // @ApiNotFoundResponse()
  // @Patch(':id')
  // async update(
  //   @Param('id') id: string,
  //   @Body() updateStaffDto: UpdateStaffDto,
  // ): Promise<Staff | object> {
  //   return this.staffService.update(+id, updateStaffDto);
  // }
  //
  // @Public()
  // @ApiOkResponse({ description: 'Staff deleted successfully' })
  // @ApiNotFoundResponse()
  // @Delete(':id')
  // async remove(@Param('id') id: string): Promise<Staff | object> {
  //   return this.staffService.remove(+id);
  // }
}
