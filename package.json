{"name": "green-api", "version": "0.0.1", "description": "", "author": "<PERSON>", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "typeorm:run-migrations": "npm run typeorm migration:run -- -d ./src/config/datasource.ts", "typeorm:generate-migration": "cross-var npm run typeorm -- -d ./src/config/datasource.ts migration:generate ./src/database/migrations/%npm_config_name%", "typeorm:create-migration": "cross-var npm run typeorm -- migration:create ./src/database/migrations/%npm_config_name%", "typeorm:revert-migration": "npm run typeorm -- -d ./src/config/datasource.ts migration:revert", "drop:database": "npm run typeorm schema:drop", "db:seed": "ts-node -r tsconfig-paths/register ./src/seed.ts"}, "dependencies": {"@automapper/classes": "^8.8.1", "@automapper/core": "^8.8.1", "@automapper/nestjs": "^8.8.1", "@casl/ability": "6", "@keyv/redis": "^4.2.0", "@nestjs-modules/mailer": "^1.6.1", "@nestjs/axios": "^3.1.3", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.6", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.1", "@nestjs/throttler": "^6.3.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.15", "@nestlab/google-recaptcha": "^3.8.0", "@onesignal/node-onesignal": "5.0.0-alpha-02", "@types/bcrypt": "^5.0.2", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "buffer-to-stream": "^1.0.0", "cache-manager": "^5.7.6", "cacheable": "^1.8.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.5.1", "cron": "^3.5.0", "cross-var": "^1.1.0", "dotenv": "^16.5.0", "dynamic-string": "^0.1.2", "handlebars": "^4.7.8", "hbs": "^4.2.0", "jsonwebtoken": "^9.0.2", "keyv": "^5.2.3", "lodash": "^4.17.21", "luxon": "^3.5.0", "mime-type": "^5.0.2", "mime-types": "^2.1.35", "nestjs-i18n": "^10.5.0", "nestjs-mailer": "^1.0.1", "nestjs-paginate": "^10.0.0", "nestjs-typeorm-paginate": "^4.0.4", "node-fetch": "^2.6.7", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "^0.1.13", "rimraf": "^6.0.1", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "stripe": "^18.0.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "typeorm-transactional": "^0.5.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/jest": "29.5.14", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/node": "^22.10.5", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}