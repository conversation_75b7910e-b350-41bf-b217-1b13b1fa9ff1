import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { Injectable } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager, Repository } from 'typeorm';
import { Staff } from './entities/staff.entity';

@Injectable()
export class StaffService implements EntityServiceStrategy<Staff> {
  constructor(
    @InjectRepository(Staff) private readonly staffRepository: Repository<Staff>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(StaffService.name);
  }

  async create(data: Staff): Promise<Staff> {
    return await this.staffRepository.save(data);
  }

  modify(id: number, data: Staff): Promise<Staff> {
    throw new Error('Method not implemented.');
  }

  findByPk(id: number): Promise<Staff> {
    throw new Error('Method not implemented.');
  }

  activate(ids: number[]): Promise<void> {
    throw new Error('Method not implemented.');
  }

  deactivate(ids: number[]): Promise<void> {
    throw new Error('Method not implemented.');
  }
}
