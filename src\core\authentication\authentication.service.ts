import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EventName } from '@common/enumerations/event_name.enum';
import { OtpType } from '@common/enumerations/otp_type.enum';
import { ProfileStatus } from '@common/enumerations/profile_status.enum';
import { ProfileType } from '@common/enumerations/profile_type.enum';
import { LoggerService } from '@common/logger/logger.service';
import { CoreConstants } from '@common/utils/core.constants';
import { CoreUtils } from '@common/utils/core.utils';
import { CustomerService } from '@core/customer/customer.service';
import { IntegrationService } from '@core/integration/integration.service';
import { OtpService } from '@core/otp/otp.service';
import { Profile } from '@core/profile/entities/profile.entity';
import { ProfileService } from '@core/profile/profile.service';
import { Staff } from '@core/staff/entities/staff.entity';
import { StaffService } from '@core/staff/staff.service';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import { InjectEntityManager } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager } from 'typeorm';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { Tokens } from './types/token.type';
import { Customer } from '@core/customer/entities/customer.entity';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AuthenticationService {
  constructor(
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly i18n: I18nService,
    private readonly profileService: ProfileService,
    private readonly customerService: CustomerService,
    private readonly staffService: StaffService,
    private readonly otpService: OtpService,
    private readonly eventEmitter: EventEmitter2,
    private readonly integrationSvc: IntegrationService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {
    this.logger.setContext(AuthenticationService.name);
  }

  private async createTokens(userId: string | number, email: string): Promise<Tokens> {
    const payload = { sub: userId, email };
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        privateKey: this.configService.get<string>('keys.secret'),
        expiresIn: '15m',
      }),
      this.jwtService.signAsync(payload, {
        privateKey: this.configService.get<string>('keys.secret'),
        expiresIn: '7d',
      }),
    ]);
    return { accessToken, refreshToken };
  }

  async signin(emailOrPhoneNumber: string, password: string) {
    //TODO: Refactor this.
    const profile = await this.entityManager.findOne(Profile, {
      where: [{ email: emailOrPhoneNumber }, { phoneNumber: emailOrPhoneNumber }],
    });

    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    }

    await this.profileService.checkProfileEligibility(profile);

    const isPasswordMatch = CoreUtils.passwordMatch(password, profile.password);
    if (!isPasswordMatch) {
      profile.failedLoginCount += 1;
      if (profile.failedLoginCount > CoreConstants.MAX_LOGIN_REATTEMPT) {
        profile.profileStatus = ProfileStatus.SUSPENDED;
      }
      const attemptsRemaining = CoreConstants.MAX_LOGIN_REATTEMPT - profile.failedLoginCount;
      await this.entityManager.save(profile);
      throw new BadRequestException(this.i18n.t('message.errors.password_incorrect', { args: { attemptsRemaining } }));
    }

    profile.failedLoginCount = 0;
    const { accessToken, refreshToken } = await this.createTokens(profile.id, profile.email);

    return {
      accessToken,
      refreshToken,
      profile,
    };
  }

  @Transactional()
  async signup(profile: Profile) {
      profile.password = CoreUtils.hashPassword(profile.password);

      // Use the transaction's manager
      const createdProfile = await this.profileService.create(profile);

      if (profile.profileType === ProfileType.CLIENT) {
        const customer = new Customer();
        // customer.profile = createdProfile;
        // await manager.save(customer);
      }

      if (profile.profileType === ProfileType.STAFF) {
        const staff = new Staff();
        staff.profile = createdProfile;
        // await manager.save(Staff, staff);
        await this.staffService.create(staff);
      }

      const otp = await this.otpService.createOtp(createdProfile.id, OtpType.ACCOUNT_VERIFICATION);

      this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
        recipient: profile.email,
        subject: 'Account Verification',
        message: `Your Verification OTP is ${otp}. It expires in ${CoreConstants.OTP_TTL} minutes.`,
      });
  }

  async verifyAccount(email: string, otp: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    }

    await this.otpService.verifyOtp(profile.id, OtpType.ACCOUNT_VERIFICATION, otp);
    await this.profileService.markProfileVerified(email);
  }

  async resendVerificationOtp(email: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Profile' } }));
    }

    if (profile.verified) {
      throw new BadRequestException(this.i18n.t('message.errors.already_verified', { args: { entity: 'Profile' } }));
    }

    const otp = await this.otpService.createOtp(profile.id, OtpType.ACCOUNT_VERIFICATION);
    this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
      recipient: email,
      subject: 'Account Verification',
      message: `Your account verification OTP is ${otp}. It expires in ${CoreConstants.OTP_TTL} minutes.`,
    });
  }

  /**
   * Send a link to reset password to user email.
   * @param email - The email of the user.
   */
  async forgotPassword(email: string) {
    const profile = await this.profileService.findByEmail(email);
    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Email' } }));
    }

    await this.profileService.checkProfileEligibility(profile);

    const otp = await this.otpService.createOtp(profile.id, OtpType.PASSWORD_RESET);
    this.eventEmitter.emit(EventName.EMAIL_NOTIFICATION, {
      recipient: email,
      subject: 'Password Reset',
      message: `Your password reset OTP is ${otp}. It expires in ${CoreConstants.OTP_TTL} minutes.`,
    });
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { email, otp, newPassword } = resetPasswordDto;
    const profile = await this.profileService.findByEmail(email);

    if (!profile) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Email' } }));
    }

    await this.profileService.checkProfileEligibility(profile);
    await this.otpService.verifyOtp(profile.id, OtpType.PASSWORD_RESET, otp);

    profile.password = CoreUtils.hashPassword(newPassword);
    profile.failedLoginCount = 0;
    profile.passwordResetDate = new Date();

    await this.profileService.update(profile);
  }
}
