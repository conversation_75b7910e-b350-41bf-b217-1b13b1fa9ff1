import { PaginatedResponseDto } from '@common/dto/paginated-response.dto';
import { PaginationDto } from '@common/dto/pagination.dto';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/pattern/entity.service.strategy';
import { Injectable } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { I18nService } from 'nestjs-i18n';
import { EntityManager, Repository } from 'typeorm';
import { Customer } from './entities/customer.entity';

@Injectable()
export class CustomerService implements EntityServiceStrategy<Customer> {
  constructor(
    @InjectRepository(Customer) private readonly customerRepository: Repository<Customer>,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(CustomerService.name);
  }
  
  async create(data: Customer): Promise<Customer> {
    return await this.customerRepository.save(data)
  }
 
  modify(id: number, data: Customer): Promise<Customer> {
    throw new Error('Method not implemented.');
  }
  findByPk(id: number): Promise<Customer> {
    throw new Error('Method not implemented.');
  }
  activate(ids: number[]): Promise<void> {
    throw new Error('Method not implemented.');
  }
  deactivate(ids: number[]): Promise<void> {
    throw new Error('Method not implemented.');
  }
  table?<R = Customer>(paginationDto: PaginationDto, ...args: Array<string | number>): Promise<PaginatedResponseDto<R>> {
    throw new Error('Method not implemented.');
  }
}